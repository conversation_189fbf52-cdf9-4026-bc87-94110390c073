// FocusGuard Pro - Focus Mode JavaScript

class FocusModePopup {
  constructor() {
    this.isActive = false;
    this.isPaused = false;
    this.isBreak = false;
    this.currentTime = 25 * 60; // 25 minutes in seconds
    this.totalTime = 25 * 60;
    this.timer = null;
    this.sessionStartTime = null;
    this.pausedTime = 0;
    this.sessionsCompleted = 0;
    this.workDuration = 25;
    this.breakDuration = 5;

    this.init();
  }

  init() {
    // Load settings from localStorage
    this.loadSettings();

    // Setup event listeners
    this.setupEventListeners();

    // Initialize UI
    this.updateDisplay();
    this.updateProgressRing();
    this.updateControls();

    // Load any existing session
    this.loadSession();
  }

  loadSettings() {
    try {
      const savedSettings = localStorage.getItem('focusGuardSettings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        if (settings.focusMode) {
          this.workDuration = settings.focusMode.workDuration || 25;
          this.breakDuration = settings.focusMode.breakDuration || 5;
        }
      }
    } catch (error) {
      console.warn('Failed to load settings:', error);
    }
  }

  saveSettings() {
    try {
      const settings = {
        focusMode: {
          workDuration: this.workDuration,
          breakDuration: this.breakDuration
        }
      };
      localStorage.setItem('focusGuardSettings', JSON.stringify(settings));
    } catch (error) {
      console.warn('Failed to save settings:', error);
    }
  }

  loadSession() {
    try {
      const sessionData = localStorage.getItem('focusGuardCurrentSession');
      if (sessionData) {
        const data = JSON.parse(sessionData);

        // Check if session is still valid (not older than 2 hours)
        if (Date.now() - data.timestamp < 2 * 60 * 60 * 1000) {
          this.isActive = data.isActive;
          this.isPaused = data.isPaused;
          this.isBreak = data.isBreak;
          this.currentTime = data.currentTime;
          this.totalTime = data.totalTime;
          this.sessionStartTime = data.sessionStartTime;
          this.pausedTime = data.pausedTime;
          this.sessionsCompleted = data.sessionsCompleted;

          // Resume timer if session was active and not paused
          if (this.isActive && !this.isPaused) {
            this.startTimer();
          }

          this.updateDisplay();
          this.updateProgressRing();
          this.updateControls();
          return true;
        }
      }
    } catch (error) {
      console.warn('Failed to load session:', error);
    }
    return false;
  }

  saveSession() {
    try {
      const sessionData = {
        isActive: this.isActive,
        isPaused: this.isPaused,
        isBreak: this.isBreak,
        currentTime: this.currentTime,
        totalTime: this.totalTime,
        sessionStartTime: this.sessionStartTime,
        pausedTime: this.pausedTime,
        sessionsCompleted: this.sessionsCompleted,
        timestamp: Date.now()
      };
      localStorage.setItem('focusGuardCurrentSession', JSON.stringify(sessionData));
    } catch (error) {
      console.warn('Failed to save session:', error);
    }
  }

  clearSession() {
    try {
      localStorage.removeItem('focusGuardCurrentSession');
    } catch (error) {
      console.warn('Failed to clear session:', error);
    }
  }

  setupEventListeners() {
    // Control buttons
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const stopBtn = document.getElementById('stopBtn');
    const closeBtn = document.getElementById('closeBtn');

    if (startBtn) startBtn.addEventListener('click', () => this.startSession());
    if (pauseBtn) pauseBtn.addEventListener('click', () => this.togglePause());
    if (stopBtn) stopBtn.addEventListener('click', () => this.stopSession());
    if (closeBtn) closeBtn.addEventListener('click', () => this.closeModal());

    // Settings
    const workDuration = document.getElementById('workDuration');
    const breakDuration = document.getElementById('breakDuration');

    if (workDuration) {
      workDuration.addEventListener('change', (e) => {
        this.workDuration = parseInt(e.target.value);
        if (!this.isActive) {
          this.currentTime = this.workDuration * 60;
          this.totalTime = this.workDuration * 60;
          this.updateDisplay();
          this.updateProgressRing();
        }
        this.saveSettings();
      });
    }

    if (breakDuration) {
      breakDuration.addEventListener('change', (e) => {
        this.breakDuration = parseInt(e.target.value);
        this.saveSettings();
      });
    }
  }

  closeModal() {
    // Close the focus mode window
    window.close();
  }

  startSession() {
    this.isActive = true;
    this.isPaused = false;
    this.isBreak = false;
    this.currentTime = this.workDuration * 60;
    this.totalTime = this.workDuration * 60;
    this.sessionStartTime = Date.now();
    this.pausedTime = 0;

    this.startTimer();
    this.updateDisplay();
    this.updateProgressRing();
    this.updateControls();
    this.saveSession();

    this.showNotification('Focus session started!', 'success');
  }

  togglePause() {
    if (this.isPaused) {
      this.resumeSession();
    } else {
      this.pauseSession();
    }
  }

  pauseSession() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      this.isPaused = true;
      this.pausedTime = Date.now();
      this.updateControls();
      this.saveSession();
      this.showNotification('Session paused', 'info');
    }
  }

  resumeSession() {
    if (this.isPaused) {
      this.isPaused = false;
      // Adjust session start time to account for paused time
      if (this.pausedTime) {
        const pauseDuration = Date.now() - this.pausedTime;
        this.sessionStartTime += pauseDuration;
      }
      this.startTimer();
      this.updateControls();
      this.saveSession();
      this.showNotification('Session resumed', 'success');
    }
  }

  stopSession() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    this.isActive = false;
    this.isPaused = false;
    this.isBreak = false;
    this.currentTime = this.workDuration * 60;
    this.totalTime = this.workDuration * 60;
    this.sessionStartTime = null;
    this.pausedTime = 0;

    this.updateDisplay();
    this.updateProgressRing();
    this.updateControls();
    this.clearSession();
    this.showNotification('Focus session stopped', 'info');
  }

  startTimer() {
    if (this.timer) {
      clearInterval(this.timer);
    }

    this.timer = setInterval(() => {
      this.currentTime--;

      if (this.currentTime <= 0) {
        this.handleSessionEnd();
      } else {
        this.updateDisplay();
        this.updateProgressRing();
        this.saveSession();
      }
    }, 1000);
  }

  handleSessionEnd() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    if (this.isBreak) {
      // Break ended, stop focus mode
      this.stopSession();
      this.showNotification('Break finished! Ready for another session?', 'success');
    } else {
      // Work session ended, start break
      this.sessionsCompleted++;
      this.isBreak = true;
      this.currentTime = this.breakDuration * 60;
      this.totalTime = this.breakDuration * 60;
      this.sessionStartTime = Date.now();

      this.startTimer();
      this.updateDisplay();
      this.updateProgressRing();
      this.updateControls();
      this.saveSession();
      this.showNotification('Great work! Time for a break.', 'success');
    }
  }

  updateDisplay() {
    const timeText = document.getElementById('timeText');
    const sessionType = document.getElementById('sessionType');

    if (timeText) {
      const minutes = Math.floor(this.currentTime / 60);
      const seconds = this.currentTime % 60;
      timeText.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    if (sessionType) {
      if (this.isActive) {
        if (this.isPaused) {
          sessionType.textContent = this.isBreak ? 'BREAK PAUSED' : 'WORK PAUSED';
        } else {
          sessionType.textContent = this.isBreak ? 'BREAK TIME' : 'WORK SESSION';
        }
      } else {
        sessionType.textContent = 'READY TO FOCUS';
      }
    }

    // Update settings display
    const workDuration = document.getElementById('workDuration');
    const breakDuration = document.getElementById('breakDuration');

    if (workDuration) workDuration.value = this.workDuration;
    if (breakDuration) breakDuration.value = this.breakDuration;
  }

  updateProgressRing() {
    const progressCircle = document.getElementById('progressCircle');

    if (!progressCircle) {
      return;
    }

    // Get the actual radius from the circle element
    const radius = parseFloat(progressCircle.getAttribute('r')) || 90;
    const circumference = 2 * Math.PI * radius;
    const progress = (this.totalTime - this.currentTime) / this.totalTime;
    const offset = circumference - (progress * circumference);

    progressCircle.style.strokeDasharray = circumference;
    progressCircle.style.strokeDashoffset = offset;

    // Update progress circle color based on session type
    if (this.isActive) {
      if (this.isBreak) {
        progressCircle.style.stroke = '#f56565'; // Red for break
      } else {
        progressCircle.style.stroke = '#48bb78'; // Green for work
      }
    } else {
      progressCircle.style.stroke = '#667eea'; // Blue for ready state
    }
  }

  updateControls() {
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const stopBtn = document.getElementById('stopBtn');

    if (!startBtn || !pauseBtn || !stopBtn) {
      return;
    }

    if (this.isActive) {
      startBtn.classList.add('hidden');

      if (this.isPaused) {
        pauseBtn.innerHTML = '<span class="btn-icon">▶️</span>Resume';
        pauseBtn.className = 'control-btn start-btn'; // Use green color for resume
      } else {
        pauseBtn.innerHTML = '<span class="btn-icon">⏸️</span>Pause';
        pauseBtn.className = 'control-btn pause-btn'; // Use orange color for pause
      }

      pauseBtn.classList.remove('hidden');
      stopBtn.classList.remove('hidden');
    } else {
      startBtn.classList.remove('hidden');
      pauseBtn.classList.add('hidden');
      stopBtn.classList.add('hidden');
    }

    // Update visual state
    const container = document.querySelector('.popup-container');
    if (container) {
      container.classList.remove('active-session', 'break-mode', 'paused');

      if (this.isActive) {
        if (this.isPaused) {
          container.classList.add('paused');
        } else if (this.isBreak) {
          container.classList.add('break-mode');
        } else {
          container.classList.add('active-session');
        }
      }
    }
  }

  showNotification(message, type = 'info') {
    // Remove any existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notif => notif.remove());

    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;

    switch (type) {
      case 'success':
        notification.style.background = '#48bb78';
        break;
      case 'error':
        notification.style.background = '#f56565';
        break;
      case 'warning':
        notification.style.background = '#ed8936';
        break;
      case 'info':
      default:
        notification.style.background = '#667eea';
        break;
    }

    document.body.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 10);

    // Auto-remove after delay
    setTimeout(() => {
      if (notification.parentNode) {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }
    }, 3000);
  }

  rotateFocusTips() {
    const tips = [
      "Remove distractions from your workspace and focus on one task at a time.",
      "Take deep breaths and set a clear intention for this work session.",
      "Turn off notifications and put your phone in another room.",
      "Break large tasks into smaller, manageable chunks.",
      "Use the two-minute rule: if it takes less than 2 minutes, do it now.",
      "Stay hydrated and maintain good posture while working.",
      "Celebrate small wins and progress made during each session."
    ];

    const tipElement = document.getElementById('focusTip');
    if (!tipElement) return;

    let currentTip = 0;

    const rotateTip = () => {
      currentTip = (currentTip + 1) % tips.length;
      tipElement.textContent = tips[currentTip];
    };

    // Change tip every 30 seconds
    setInterval(rotateTip, 30000);
  }
}

// Initialize focus mode when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.FocusModePopup = FocusModePopup;
  if (!window.focusModeInstance) {
    window.focusModeInstance = new FocusModePopup();
    // Start focus tips rotation
    window.focusModeInstance.rotateFocusTips();
  }
});

// Also make it available for modal initialization
window.FocusModePopup = FocusModePopup;
