<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus Mode - FocusGuard Pro</title>
    <link rel="stylesheet" href="popup.css">
    <link rel="stylesheet" href="focus-mode.css">
</head>

<body>
    <div class="popup-container">
        <!-- Header -->
        <header class="popup-header">
            <div class="logo">
                <div class="shield-icon">🛡️</div>
                <h1>Focus Mode</h1>
            </div>
            <div class="header-controls">
                <button class="close-btn" id="closeBtn">×</button>
            </div>
        </header>

        <!-- Focus Mode Content -->
        <div class="focus-content">
            <!-- Timer Display -->
            <div class="timer-section">
                <div class="timer-circle" id="timerCircle">
                    <div class="timer-display">
                        <div class="time-text" id="timeText">25:00</div>
                        <div class="session-type" id="sessionType">Ready to Focus</div>
                    </div>
                </div>

                <div class="progress-ring" id="progressRing">
                    <svg width="200" height="200">
                        <circle cx="100" cy="100" r="90" stroke="#edf2f7" stroke-width="8" fill="none" />
                        <circle cx="100" cy="100" r="90" stroke="#667eea" stroke-width="8" fill="none"
                            stroke-dasharray="565.48" stroke-dashoffset="565.48" transform="rotate(-90 100 100)"
                            id="progressCircle" />
                    </svg>
                </div>
            </div>

            <!-- Control Buttons -->
            <div class="controls">
                <button class="control-btn start-btn" id="startBtn">
                    <span class="btn-icon">▶️</span>
                    Start Focus
                </button>
                <button class="control-btn pause-btn hidden" id="pauseBtn">
                    <span class="btn-icon">⏸️</span>
                    Pause
                </button>
                <button class="control-btn stop-btn hidden" id="stopBtn">
                    <span class="btn-icon">⏹️</span>
                    Stop
                </button>
            </div>

            <!-- Session Info -->
            <div class="session-info">
                <div class="info-item">
                    <span class="info-label">Sessions Completed</span>
                    <span class="info-value" id="sessionsCount">0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Current Session</span>
                    <span class="info-value" id="currentSession">1</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Next Break</span>
                    <span class="info-value" id="nextBreak">5 min</span>
                </div>
            </div>

            <!-- Settings Quick Access -->
            <div class="quick-settings">
                <h3>Quick Settings</h3>
                <div class="setting-row">
                    <label>Work Duration</label>
                    <select id="workDuration" class="setting-select">
                        <option value="15">15 min</option>
                        <option value="20">20 min</option>
                        <option value="25" selected>25 min</option>
                        <option value="30">30 min</option>
                        <option value="45">45 min</option>
                    </select>
                </div>
                <div class="setting-row">
                    <label>Break Duration</label>
                    <select id="breakDuration" class="setting-select">
                        <option value="5" selected>5 min</option>
                        <option value="10">10 min</option>
                        <option value="15">15 min</option>
                    </select>
                </div>
                <div class="setting-row">
                    <label>Auto-start breaks</label>
                    <div class="toggle-switch active" id="autoBreaksToggle"></div>
                </div>
            </div>

            <!-- Focus Tips -->
            <div class="focus-tips">
                <h3>💡 Focus Tips</h3>
                <div class="tip" id="focusTip">
                    Remove distractions from your workspace and focus on one task at a time.
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <p>Starting focus session...</p>
        </div>
    </div>

    <script src="../utils/storage.js"></script>
    <script src="focus-mode.js"></script>
</body>

</html>