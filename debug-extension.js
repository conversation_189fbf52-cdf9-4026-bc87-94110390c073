// FocusGuard Pro Debug Script
// Run this in the browser console to debug extension issues

console.log('🔍 FocusGuard Pro Debug Script Starting...');

async function debugExtension() {
    console.log('\n=== EXTENSION DEBUG INFORMATION ===');
    
    // Check if extension API is available
    if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.error('❌ Chrome extension API not available');
        return;
    }
    
    console.log('✅ Chrome extension API available');
    
    try {
        // Test basic communication
        console.log('\n--- Testing Extension Communication ---');
        const response = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getBlockedSites' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(response);
                }
            });
        });
        
        console.log('✅ Extension communication working');
        console.log('📊 Extension Data:', response);
        
        if (response) {
            console.log(`📋 Blocked Sites (${response.sites?.length || 0}):`, response.sites);
            console.log('🔛 Extension Enabled:', response.isEnabled);
            console.log('🎯 Focus Mode:', response.focusMode);
            console.log('📈 Usage Tracking:', response.usageTracking);
            console.log('⏰ Temporary Unblocks:', response.temporaryUnblocks);
        }
        
        // Test adding a site
        console.log('\n--- Testing Add Site Functionality ---');
        const testDomain = 'test-example.com';
        
        const addResponse = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ 
                action: 'addBlockedSite', 
                url: testDomain 
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(response);
                }
            });
        });
        
        console.log('✅ Add site test:', addResponse);
        
        // Check if site was added
        const updatedResponse = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getBlockedSites' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(response);
                }
            });
        });
        
        const wasAdded = updatedResponse.sites?.includes(testDomain);
        console.log(`${wasAdded ? '✅' : '❌'} Test site added:`, wasAdded);
        
        if (wasAdded) {
            // Remove test site
            await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({ 
                    action: 'removeBlockedSite', 
                    url: testDomain 
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve(response);
                    }
                });
            });
            console.log('🧹 Test site removed');
        }
        
        // Test site blocking check
        console.log('\n--- Testing Site Blocking Logic ---');
        const testUrls = [
            'https://example.com',
            'https://facebook.com',
            'https://google.com'
        ];
        
        for (const url of testUrls) {
            const checkResponse = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({ 
                    action: 'checkSiteStatus', 
                    url: url 
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve(response);
                    }
                });
            });
            
            console.log(`🔍 ${url}: ${checkResponse.isBlocked ? 'BLOCKED' : 'ALLOWED'}`);
        }
        
        // Check storage directly
        console.log('\n--- Checking Chrome Storage ---');
        const storageData = await new Promise((resolve) => {
            chrome.storage.local.get(null, (data) => {
                resolve(data);
            });
        });
        
        console.log('💾 Raw Storage Data:', storageData);
        
        // Test permissions
        console.log('\n--- Checking Permissions ---');
        const permissions = await chrome.permissions.getAll();
        console.log('🔐 Extension Permissions:', permissions);
        
        console.log('\n=== DEBUG COMPLETE ===');
        console.log('✅ Extension appears to be working correctly');
        
    } catch (error) {
        console.error('❌ Extension Error:', error);
        console.log('\n🔧 Troubleshooting Steps:');
        console.log('1. Check if extension is enabled in chrome://extensions/');
        console.log('2. Try reloading the extension');
        console.log('3. Check for JavaScript errors in background script');
        console.log('4. Verify manifest.json permissions');
    }
}

// Function to test blocking on current page
function testCurrentPageBlocking() {
    console.log('\n=== TESTING CURRENT PAGE BLOCKING ===');
    const currentUrl = window.location.href;
    console.log('🌐 Current URL:', currentUrl);
    
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({ 
            action: 'checkSiteStatus', 
            url: currentUrl 
        }, (response) => {
            if (response) {
                console.log(`🔍 Current page blocking status: ${response.isBlocked ? 'BLOCKED' : 'ALLOWED'}`);
                
                if (response.isBlocked) {
                    console.log('⚠️ This page should be blocked but is still accessible');
                    console.log('💡 This might indicate a blocking issue');
                } else {
                    console.log('✅ This page is not blocked (as expected)');
                }
            }
        });
    }
}

// Function to manually trigger blocking test
function triggerBlockingTest(domain) {
    console.log(`\n=== MANUAL BLOCKING TEST FOR ${domain} ===`);
    
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        // First add the site to blocked list
        chrome.runtime.sendMessage({ 
            action: 'addBlockedSite', 
            url: domain 
        }, (response) => {
            console.log('Add site response:', response);
            
            // Then try to navigate to it
            console.log(`🔗 Try visiting: https://${domain}`);
            console.log('💡 Open this URL in a new tab to test blocking');
        });
    }
}

// Run the debug script
debugExtension();

// Make functions available globally for manual testing
window.debugFocusGuard = debugExtension;
window.testCurrentPageBlocking = testCurrentPageBlocking;
window.triggerBlockingTest = triggerBlockingTest;

console.log('\n🛠️ Manual Testing Functions Available:');
console.log('- debugFocusGuard() - Run full debug');
console.log('- testCurrentPageBlocking() - Test current page');
console.log('- triggerBlockingTest("domain.com") - Test specific domain');
