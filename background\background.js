// FocusGuard Pro - Background Service Worker
// Phase 1: Basic site blocking functionality

class FocusGuardBackground {
  constructor() {
    this.blockedSites = new Set();
    this.isEnabled = true;
    this.temporaryUnblocks = new Map(); // domain -> unblock end time
    this.usageTracking = new Map(); // domain -> daily usage in minutes
    this.dailyLimits = new Map(); // domain -> limit in minutes
    this.focusMode = {
      isActive: false,
      isBreak: false,
      sessionStartTime: null,
      sessionDuration: 25, // minutes
      breakDuration: 5, // minutes
      sessionsCompleted: 0
    };
    this.timers = new Map(); // active timers

    // Phase 4: Content Filtering & Protection
    this.adultContentSites = new Set();
    this.blockedKeywords = new Set();
    this.contentFiltering = {
      enableAdultContentBlocking: true,
      enableKeywordBlocking: true,
      strictMode: false,
      customKeywords: [],
      forceSafeSearch: true
    };

    // Phase 4: Safety mechanisms for disabling protection
    this.protectionDisabling = {
      isCountingDown: false,
      countdownStartTime: null,
      countdownDuration: 60 * 60 * 1000, // 1 hour in milliseconds
      temporaryDisableEndTime: null,
      temporaryDisableDuration: 10 * 60 * 1000 // 10 minutes in milliseconds
    };

    this.init();
  }

  async init() {
    // Load saved settings and blocked sites
    await this.loadSettings();

    // Phase 4: Initialize content filtering
    this.initializeContentFiltering();

    // Set up event listeners
    this.setupEventListeners();

    // Initialize blocking rules
    await this.updateBlockingRules();

    console.log('FocusGuard Pro initialized with', this.blockedSites.size, 'blocked sites');
    console.log('Blocked sites:', Array.from(this.blockedSites));
    console.log('Adult content sites loaded:', this.adultContentSites.size);
    console.log('Extension enabled:', this.isEnabled);
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get([
        'blockedSites',
        'isEnabled',
        'settings',
        'temporaryUnblocks',
        'usageTracking',
        'dailyLimits',
        'focusMode',
        'masterPassword',
        'adultContentSites',
        'blockedKeywords',
        'contentFiltering',
        'protectionDisabling'
      ]);

      this.blockedSites = new Set(result.blockedSites || []);
      this.isEnabled = result.isEnabled !== false; // Default to true
      this.settings = result.settings || this.getDefaultSettings();
      this.temporaryUnblocks = new Map(result.temporaryUnblocks || []);
      this.usageTracking = new Map(result.usageTracking || []);
      this.dailyLimits = new Map(result.dailyLimits || []);
      this.focusMode = { ...this.focusMode, ...(result.focusMode || {}) };
      this.masterPassword = result.masterPassword || null;

      // Phase 4: Load content filtering settings
      this.adultContentSites = new Set(result.adultContentSites || []);
      this.blockedKeywords = new Set(result.blockedKeywords || []);
      this.contentFiltering = { ...this.contentFiltering, ...(result.contentFiltering || {}) };
      this.protectionDisabling = { ...this.protectionDisabling, ...(result.protectionDisabling || {}) };

      // Clean up expired temporary unblocks
      this.cleanupExpiredUnblocks();

      // Phase 4: Clean up expired protection disables
      this.cleanupExpiredProtectionDisables();

      // Reset daily usage if it's a new day
      this.resetDailyUsageIfNeeded();

      console.log('Settings loaded:', {
        blockedSites: Array.from(this.blockedSites),
        isEnabled: this.isEnabled,
        focusMode: this.focusMode
      });
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  getDefaultSettings() {
    return {
      defaultBlockDuration: '5days',
      showNotifications: true,
      workInIncognito: true,
      enableTimeTracking: true,
      enableTimeLimits: false,
      defaultDailyLimit: 120, // minutes
      focusMode: {
        workDuration: 25,
        breakDuration: 5,
        autoStartBreaks: true
      },
      passwordProtection: false,
      // Phase 4: Content filtering defaults
      contentFiltering: {
        enableAdultContentBlocking: true,
        enableKeywordBlocking: true,
        strictMode: false,
        customKeywords: []
      }
    };
  }

  // Phase 4: Content Filtering Initialization
  initializeContentFiltering() {
    // Initialize adult content database
    this.loadAdultContentDatabase();

    // Initialize default blocked keywords
    this.loadDefaultBlockedKeywords();

    // Merge custom keywords with defaults
    this.mergeCustomKeywords();
  }

  loadAdultContentDatabase() {
    // Pre-loaded database of known adult content domains
    const adultSites = [
      // Major adult content sites (sample - in production this would be more comprehensive)
      'pornhub.com', 'xvideos.com', 'xnxx.com', 'redtube.com', 'youporn.com',
      'tube8.com', 'spankbang.com', 'xhamster.com', 'beeg.com', 'sex.com',
      'porn.com', 'chaturbate.com', 'cam4.com', 'livejasmin.com', 'stripchat.com',
      'onlyfans.com', 'manyvids.com', 'clips4sale.com', 'iwantclips.com',
      // Dating/hookup sites that may contain adult content
      'adultfriendfinder.com', 'ashley-madison.com', 'fling.com', 'benaughty.com',
      // Adult forums and communities
      'reddit.com/r/nsfw', 'reddit.com/r/gonewild', 'reddit.com/r/porn',
      // Image hosting with adult content
      'imagefap.com', 'motherless.com', 'heavy-r.com'
    ];

    // Add to adult content sites set
    adultSites.forEach(site => this.adultContentSites.add(site));

    console.log('Adult content database loaded with', this.adultContentSites.size, 'sites');
  }

  loadDefaultBlockedKeywords() {
    // Default blocked keywords for search filtering
    const defaultKeywords = [
      // Explicit terms
      'porn', 'sex', 'nude', 'naked', 'xxx', 'adult', 'erotic', 'nsfw',
      'masturbation', 'orgasm', 'fetish', 'bdsm', 'escort', 'prostitute',
      // Gambling terms
      'casino', 'gambling', 'poker', 'blackjack', 'slots', 'betting',
      'lottery', 'jackpot', 'roulette', 'baccarat',
      // Violence/harmful content
      'suicide', 'self-harm', 'cutting', 'anorexia', 'bulimia',
      // Drugs
      'cocaine', 'heroin', 'methamphetamine', 'ecstasy', 'lsd'
    ];

    defaultKeywords.forEach(keyword => this.blockedKeywords.add(keyword.toLowerCase()));

    console.log('Default blocked keywords loaded:', this.blockedKeywords.size, 'keywords');
  }

  mergeCustomKeywords() {
    // Add custom keywords from settings
    if (this.contentFiltering.customKeywords) {
      this.contentFiltering.customKeywords.forEach(keyword => {
        this.blockedKeywords.add(keyword.toLowerCase());
      });
    }
  }

  // Phase 2: Timer and Usage Tracking Methods
  cleanupExpiredUnblocks() {
    const now = Date.now();
    for (const [domain, endTime] of this.temporaryUnblocks) {
      if (now > endTime) {
        this.temporaryUnblocks.delete(domain);
      }
    }
    this.saveSettings();
  }

  // Phase 4: Clean up expired protection disables
  cleanupExpiredProtectionDisables() {
    const now = Date.now();

    // Check if temporary disable has expired
    if (this.protectionDisabling.temporaryDisableEndTime && now > this.protectionDisabling.temporaryDisableEndTime) {
      // Re-enable protection
      this.contentFiltering.enableAdultContentBlocking = true;
      this.contentFiltering.enableKeywordBlocking = true;
      this.protectionDisabling.temporaryDisableEndTime = null;
      this.protectionDisabling.isCountingDown = false;
      this.protectionDisabling.countdownStartTime = null;

      console.log('Adult content protection automatically re-enabled after temporary disable');

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Protection Re-enabled',
          message: 'Adult content protection has been automatically re-enabled for your safety.'
        });
      }

      this.saveSettings();
    }
  }

  resetDailyUsageIfNeeded() {
    const today = new Date().toDateString();
    const lastReset = this.settings.lastUsageReset;

    if (lastReset !== today) {
      this.usageTracking.clear();
      this.settings.lastUsageReset = today;
      this.saveSettings();
    }
  }

  async trackUsage(domain, timeSpent) {
    if (!this.settings.enableTimeTracking) return;

    const currentUsage = this.usageTracking.get(domain) || 0;
    const newUsage = currentUsage + timeSpent;
    this.usageTracking.set(domain, newUsage);

    // Check if daily limit exceeded
    const limit = this.dailyLimits.get(domain) || this.settings.defaultDailyLimit;
    if (this.settings.enableTimeLimits && newUsage >= limit) {
      // Add to blocked sites temporarily
      this.blockedSites.add(domain);

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Daily Limit Reached',
          message: `You've reached your daily limit for ${domain}`
        });
      }
    }

    await this.saveSettings();
  }

  // Phase 2: Focus Mode Methods
  async startFocusSession() {
    this.focusMode.isActive = true;
    this.focusMode.isBreak = false;
    this.focusMode.sessionStartTime = Date.now();

    // Set alarm for work session end
    chrome.alarms.create('focusSessionEnd', {
      delayInMinutes: this.focusMode.sessionDuration
    });

    // Block all sites during focus mode (optional enhancement)
    this.focusModeBlocking = true;

    await this.saveSettings();

    if (this.settings.showNotifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Focus Session Started',
        message: `${this.focusMode.sessionDuration} minute work session started`
      });
    }
  }

  async startBreak() {
    this.focusMode.isBreak = true;
    this.focusMode.sessionStartTime = Date.now();
    this.focusMode.sessionsCompleted++;

    // Set alarm for break end
    chrome.alarms.create('breakEnd', {
      delayInMinutes: this.focusMode.breakDuration
    });

    // Unblock sites during break
    this.focusModeBlocking = false;

    await this.saveSettings();

    if (this.settings.showNotifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Break Time!',
        message: `${this.focusMode.breakDuration} minute break started`
      });
    }
  }

  async stopFocusMode() {
    this.focusMode.isActive = false;
    this.focusMode.isBreak = false;
    this.focusMode.sessionStartTime = null;
    this.focusModeBlocking = false;

    // Clear alarms
    chrome.alarms.clear('focusSessionEnd');
    chrome.alarms.clear('breakEnd');

    await this.saveSettings();

    if (this.settings.showNotifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Focus Mode Stopped',
        message: `Completed ${this.focusMode.sessionsCompleted} sessions`
      });
    }
  }

  setupEventListeners() {
    // Listen for tab updates to check for blocked sites
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'loading' && tab.url) {
        this.checkAndBlockSite(tabId, tab.url);
      }
    });

    // Listen for navigation events
    chrome.webNavigation.onBeforeNavigate.addListener((details) => {
      if (details.frameId === 0) { // Main frame only
        this.checkAndBlockSite(details.tabId, details.url);
      }
    });

    // Listen for messages from popup and content scripts
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.handleFirstInstall();
      }
    });

    // Phase 2: Handle alarms for focus mode
    chrome.alarms.onAlarm.addListener((alarm) => {
      this.handleAlarm(alarm);
    });
  }

  // Phase 3: Security and Password Methods
  async setMasterPassword(password) {
    // Hash the password for security
    const hashedPassword = await this.hashPassword(password);
    this.masterPassword = hashedPassword;
    await this.saveSettings();
    return true;
  }

  async verifyMasterPassword(password) {
    if (!this.masterPassword) return false;
    const hashedInput = await this.hashPassword(password);
    return hashedInput === this.masterPassword;
  }

  async hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'focusguard_salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  generateOTP() {
    // Generate 6-digit OTP
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async createTemporaryUnblock(domain, durationMinutes, otp = null) {
    const endTime = Date.now() + (durationMinutes * 60 * 1000);
    this.temporaryUnblocks.set(domain, endTime);

    // Store OTP if provided
    if (otp) {
      this.activeOTPs = this.activeOTPs || new Map();
      this.activeOTPs.set(otp, { domain, endTime });

      // Clear OTP after 10 minutes
      setTimeout(() => {
        this.activeOTPs.delete(otp);
      }, 10 * 60 * 1000);
    }

    await this.saveSettings();

    if (this.settings.showNotifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Temporary Unblock',
        message: `${domain} unblocked for ${durationMinutes} minutes`
      });
    }
  }

  async verifyOTP(otp, domain) {
    if (!this.activeOTPs || !this.activeOTPs.has(otp)) {
      return false;
    }

    const otpData = this.activeOTPs.get(otp);
    if (otpData.domain !== domain || Date.now() > otpData.endTime) {
      this.activeOTPs.delete(otp);
      return false;
    }

    return true;
  }

  async handleAlarm(alarm) {
    switch (alarm.name) {
      case 'focusSessionEnd':
        if (this.settings.focusMode.autoStartBreaks) {
          await this.startBreak();
        } else {
          await this.stopFocusMode();
        }
        break;

      case 'breakEnd':
        await this.stopFocusMode();
        break;
    }
  }

  async handleMessage(request, _sender, sendResponse) {
    try {
      console.log('Background received message:', request.action);

      switch (request.action) {
        // Phase 1 actions
        case 'addBlockedSite':
          await this.addBlockedSite(request.url);
          sendResponse({ success: true });
          break;

        case 'removeBlockedSite':
          await this.removeBlockedSite(request.url);
          sendResponse({ success: true });
          break;

        case 'getBlockedSites':
          sendResponse({
            sites: Array.from(this.blockedSites),
            isEnabled: this.isEnabled,
            focusMode: this.focusMode,
            usageTracking: Object.fromEntries(this.usageTracking),
            temporaryUnblocks: Object.fromEntries(this.temporaryUnblocks)
          });
          break;

        case 'toggleBlocking':
          await this.toggleBlocking();
          sendResponse({ success: true, isEnabled: this.isEnabled });
          break;

        case 'checkSiteStatus':
          const isBlocked = this.isSiteBlocked(request.url);
          sendResponse({ isBlocked });
          break;

        // Phase 2 actions - Focus Mode
        case 'startFocusSession':
          console.log('Starting focus session...');
          await this.startFocusSession();
          sendResponse({ success: true, focusMode: this.focusMode });
          break;

        case 'startBreak':
          console.log('Starting break...');
          await this.startBreak();
          sendResponse({ success: true, focusMode: this.focusMode });
          break;

        case 'stopFocusMode':
          console.log('Stopping focus mode...');
          await this.stopFocusMode();
          sendResponse({ success: true, focusMode: this.focusMode });
          break;

        case 'getFocusStatus':
          console.log('Getting focus status:', this.focusMode);
          sendResponse({ success: true, focusMode: this.focusMode });
          break;

        // Phase 2 actions - Time Limits
        case 'setTimeLimit':
          this.dailyLimits.set(request.domain, request.limitMinutes);
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'getUsageStats':
          sendResponse({
            usage: Object.fromEntries(this.usageTracking),
            limits: Object.fromEntries(this.dailyLimits)
          });
          break;

        case 'updateTimeSpent':
          await this.trackUsage(request.domain, request.timeSpent);
          sendResponse({ success: true });
          break;

        // Phase 3 actions - Security
        case 'setMasterPassword':
          const passwordSet = await this.setMasterPassword(request.password);
          sendResponse({ success: passwordSet });
          break;

        case 'verifyMasterPassword':
          const isValid = await this.verifyMasterPassword(request.password);
          sendResponse({ valid: isValid });
          break;

        case 'generateOTP':
          const otp = this.generateOTP();
          sendResponse({ otp });
          break;

        case 'createTemporaryUnblock':
          await this.createTemporaryUnblock(request.domain, request.durationMinutes, request.otp);
          sendResponse({ success: true });
          break;

        case 'verifyOTP':
          const otpValid = await this.verifyOTP(request.otp, request.domain);
          sendResponse({ valid: otpValid });
          break;

        case 'getSecurityStatus':
          sendResponse({
            hasPassword: !!this.masterPassword,
            passwordProtection: this.settings.passwordProtection
          });
          break;

        // Phase 4 actions - Content Filtering
        case 'updateContentFiltering':
          this.contentFiltering = { ...this.contentFiltering, ...request.settings };
          this.mergeCustomKeywords();
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'getContentFilteringStatus':
          sendResponse({
            contentFiltering: this.contentFiltering,
            adultSitesCount: this.adultContentSites.size,
            keywordsCount: this.blockedKeywords.size
          });
          break;

        case 'addCustomKeyword':
          if (request.keyword) {
            this.blockedKeywords.add(request.keyword.toLowerCase());
            if (!this.contentFiltering.customKeywords.includes(request.keyword)) {
              this.contentFiltering.customKeywords.push(request.keyword);
            }
            await this.saveSettings();
            sendResponse({ success: true });
          } else {
            sendResponse({ success: false, error: 'No keyword provided' });
          }
          break;

        case 'removeCustomKeyword':
          if (request.keyword) {
            this.blockedKeywords.delete(request.keyword.toLowerCase());
            this.contentFiltering.customKeywords = this.contentFiltering.customKeywords.filter(
              k => k.toLowerCase() !== request.keyword.toLowerCase()
            );
            await this.saveSettings();
            sendResponse({ success: true });
          } else {
            sendResponse({ success: false, error: 'No keyword provided' });
          }
          break;

        case 'getBlockedKeywords':
          sendResponse({
            keywords: Array.from(this.blockedKeywords),
            customKeywords: this.contentFiltering.customKeywords
          });
          break;

        // Phase 4: Protection disabling with countdown
        case 'startProtectionDisableCountdown':
          this.protectionDisabling.isCountingDown = true;
          this.protectionDisabling.countdownStartTime = Date.now();
          await this.saveSettings();
          sendResponse({ success: true, countdownStartTime: this.protectionDisabling.countdownStartTime });
          break;

        case 'cancelProtectionDisableCountdown':
          this.protectionDisabling.isCountingDown = false;
          this.protectionDisabling.countdownStartTime = null;
          await this.saveSettings();
          sendResponse({ success: true });
          break;

        case 'confirmProtectionDisable':
          // Check if countdown has completed (1 hour)
          const now = Date.now();
          const countdownElapsed = now - (this.protectionDisabling.countdownStartTime || 0);

          if (countdownElapsed >= this.protectionDisabling.countdownDuration) {
            // Temporarily disable protection for 10 minutes
            this.protectionDisabling.temporaryDisableEndTime = now + this.protectionDisabling.temporaryDisableDuration;
            this.protectionDisabling.isCountingDown = false;
            this.protectionDisabling.countdownStartTime = null;

            await this.saveSettings();

            // Show warning notification
            if (this.settings.showNotifications) {
              chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'Protection Temporarily Disabled',
                message: 'Adult content protection disabled for 10 minutes. It will automatically re-enable.'
              });
            }

            sendResponse({
              success: true,
              temporaryDisableEndTime: this.protectionDisabling.temporaryDisableEndTime
            });
          } else {
            sendResponse({
              success: false,
              error: 'Countdown not completed',
              remainingTime: this.protectionDisabling.countdownDuration - countdownElapsed
            });
          }
          break;

        case 'getProtectionDisableStatus':
          sendResponse({
            protectionDisabling: this.protectionDisabling,
            isTemporarilyDisabled: this.protectionDisabling.temporaryDisableEndTime &&
              Date.now() < this.protectionDisabling.temporaryDisableEndTime
          });
          break;

        default:
          console.warn('Unknown action:', request.action);
          sendResponse({ error: 'Unknown action', action: request.action });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message, success: false });
    }
  }

  async addBlockedSite(url) {
    const domain = this.extractDomain(url);
    console.log('Adding blocked site:', url, '-> domain:', domain);

    if (domain) {
      this.blockedSites.add(domain);
      console.log('Blocked sites now:', Array.from(this.blockedSites));

      await this.saveSettings();
      await this.updateBlockingRules();

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'FocusGuard Pro',
          message: `${domain} has been blocked`
        });
      }

      console.log('Successfully added blocked site:', domain);
    } else {
      console.error('Could not extract domain from:', url);
    }
  }

  async removeBlockedSite(url) {
    const domain = this.extractDomain(url);
    if (domain && this.blockedSites.has(domain)) {
      this.blockedSites.delete(domain);
      await this.saveSettings();
      await this.updateBlockingRules();

      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'FocusGuard Pro',
          message: `${domain} has been unblocked`
        });
      }
    }
  }

  async toggleBlocking() {
    this.isEnabled = !this.isEnabled;
    await this.saveSettings();
    await this.updateBlockingRules();
  }

  extractDomain(url) {
    try {
      // If URL doesn't have protocol, add one
      if (!url.includes('://')) {
        url = 'http://' + url;
      }

      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');
      console.log('Extracted domain:', domain, 'from URL:', url);
      return domain;
    } catch (error) {
      // If URL parsing fails, try to extract domain manually
      console.log('URL parsing failed, trying manual extraction for:', url);

      // Remove protocol if present
      let domain = url.replace(/^https?:\/\//, '');

      // Remove www. if present
      domain = domain.replace(/^www\./, '');

      // Remove path and query parameters
      domain = domain.split('/')[0].split('?')[0].split('#')[0];

      // Basic validation - should contain at least one dot
      if (domain.includes('.') && domain.length > 3) {
        console.log('Manually extracted domain:', domain);
        return domain;
      }

      console.error('Could not extract domain from:', url);
      return null;
    }
  }

  isSiteBlocked(url) {
    console.log('Checking if site is blocked:', url);
    console.log('Extension enabled:', this.isEnabled);
    console.log('Blocked sites:', Array.from(this.blockedSites));

    if (!this.isEnabled) {
      console.log('Extension disabled, not blocking');
      return false;
    }

    const domain = this.extractDomain(url);
    console.log('Extracted domain:', domain);

    if (!domain) {
      console.log('No domain extracted, not blocking');
      return false;
    }

    // Skip chrome:// and extension pages
    if (url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
      console.log('Chrome internal page, not blocking');
      return false;
    }

    // Phase 4: Check adult content blocking (cannot be overridden by temporary unblocks)
    if (this.contentFiltering.enableAdultContentBlocking && this.isAdultContent(domain, url)) {
      console.log('Adult content detected, blocking site');
      return true;
    }

    // Check if temporarily unblocked
    if (this.temporaryUnblocks.has(domain)) {
      const unblockEndTime = this.temporaryUnblocks.get(domain);
      if (Date.now() < unblockEndTime) {
        console.log('Site temporarily unblocked');
        return false; // Site is temporarily unblocked
      } else {
        // Temporary unblock expired, remove it
        this.temporaryUnblocks.delete(domain);
        this.saveSettings();
        console.log('Temporary unblock expired');
      }
    }

    // Check focus mode blocking
    if (this.focusModeBlocking && this.focusMode.isActive && !this.focusMode.isBreak) {
      // During focus mode, block all sites except essential ones
      const essentialSites = ['localhost', 'chrome-extension', 'chrome://', 'about:'];
      if (!essentialSites.some(site => url.includes(site))) {
        console.log('Focus mode active, blocking site');
        return true;
      }
    }

    // Check exact match
    if (this.blockedSites.has(domain)) {
      console.log('Exact match found, blocking site');
      return true;
    }

    // Check subdomain match
    for (const blockedDomain of this.blockedSites) {
      if (domain.endsWith('.' + blockedDomain) || domain === blockedDomain) {
        console.log('Subdomain match found, blocking site');
        return true;
      }
    }

    console.log('Site not in blocked list, allowing');
    return false;
  }

  // Phase 4: Adult Content Detection
  isAdultContent(domain, url) {
    // Check if adult content protection is temporarily disabled
    if (this.protectionDisabling.temporaryDisableEndTime &&
      Date.now() < this.protectionDisabling.temporaryDisableEndTime) {
      return false; // Temporarily disabled
    }

    // Check against adult content database
    if (this.adultContentSites.has(domain)) {
      return true;
    }

    // Check for adult content patterns in domain
    const adultPatterns = [
      /porn/i, /sex/i, /xxx/i, /adult/i, /nude/i, /naked/i,
      /erotic/i, /cam/i, /escort/i, /hookup/i, /dating/i
    ];

    for (const pattern of adultPatterns) {
      if (pattern.test(domain) || pattern.test(url)) {
        return true;
      }
    }

    // Check for adult content in URL path
    const urlLower = url.toLowerCase();
    const adultKeywords = ['porn', 'sex', 'nude', 'xxx', 'adult', 'nsfw', 'erotic'];

    for (const keyword of adultKeywords) {
      if (urlLower.includes(keyword)) {
        return true;
      }
    }

    return false;
  }

  // Phase 4: Keyword Blocking for Search Engines
  containsBlockedKeywords(searchQuery) {
    if (!this.contentFiltering.enableKeywordBlocking) {
      return false;
    }

    // Check if keyword blocking is temporarily disabled
    if (this.protectionDisabling.temporaryDisableEndTime &&
      Date.now() < this.protectionDisabling.temporaryDisableEndTime) {
      return false; // Temporarily disabled
    }

    const queryLower = searchQuery.toLowerCase();

    for (const keyword of this.blockedKeywords) {
      if (queryLower.includes(keyword)) {
        console.log('Blocked keyword detected:', keyword);
        return true;
      }
    }

    return false;
  }

  // Phase 4: Force safe search on search engines
  forceSafeSearch(url) {
    if (!this.contentFiltering.forceSafeSearch) {
      return url; // Safe search not enabled
    }

    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');

      // Apply safe search parameters for different search engines
      if (domain.includes('google.com')) {
        urlObj.searchParams.set('safe', 'strict');
      } else if (domain.includes('bing.com')) {
        urlObj.searchParams.set('adlt', 'strict');
      } else if (domain.includes('yahoo.com')) {
        urlObj.searchParams.set('vm', 'r');
      } else if (domain.includes('duckduckgo.com')) {
        urlObj.searchParams.set('kp', '1'); // Strict safe search
      } else if (domain.includes('yandex.com')) {
        urlObj.searchParams.set('family', 'yes');
      }

      return urlObj.toString();
    } catch (error) {
      console.error('Error applying safe search:', error);
      return url;
    }
  }

  // Phase 4: Check if URL is a search with blocked keywords
  isBlockedSearch(url) {
    // Check major search engines
    const searchEngines = [
      { domain: 'google.com', param: 'q' },
      { domain: 'bing.com', param: 'q' },
      { domain: 'yahoo.com', param: 'p' },
      { domain: 'duckduckgo.com', param: 'q' },
      { domain: 'yandex.com', param: 'text' },
      { domain: 'baidu.com', param: 'wd' }
    ];

    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');

      for (const engine of searchEngines) {
        if (domain.includes(engine.domain)) {
          const searchQuery = urlObj.searchParams.get(engine.param);
          if (searchQuery && this.containsBlockedKeywords(searchQuery)) {
            return true;
          }
        }
      }
    } catch (error) {
      console.error('Error parsing search URL:', error);
    }

    return false;
  }

  async checkAndBlockSite(tabId, url) {
    console.log('Checking site:', url, 'Tab ID:', tabId);

    // Phase 4: Apply safe search if enabled
    const safeSearchUrl = this.forceSafeSearch(url);
    if (safeSearchUrl !== url) {
      console.log('Applying safe search redirect:', url, '->', safeSearchUrl);
      try {
        await chrome.tabs.update(tabId, { url: safeSearchUrl });
        console.log('Successfully applied safe search');
        return;
      } catch (error) {
        console.error('Error applying safe search:', error);
      }
    }

    // Phase 4: Check for blocked search queries
    if (this.isBlockedSearch(url)) {
      console.log('Blocked search query detected, redirecting:', url);

      const blockedPageUrl = chrome.runtime.getURL('blocked/blocked.html') +
        '?blocked=' + encodeURIComponent(url) + '&reason=keyword';

      try {
        await chrome.tabs.update(tabId, { url: blockedPageUrl });
        console.log('Successfully redirected blocked search to blocked page');
        return;
      } catch (error) {
        console.error('Error redirecting blocked search:', error);
      }
    }

    if (this.isSiteBlocked(url)) {
      console.log('Site is blocked, redirecting:', url);

      // Redirect to blocked page
      const blockedPageUrl = chrome.runtime.getURL('blocked/blocked.html') +
        '?blocked=' + encodeURIComponent(url);

      try {
        await chrome.tabs.update(tabId, { url: blockedPageUrl });
        console.log('Successfully redirected to blocked page');
      } catch (error) {
        console.error('Error redirecting to blocked page:', error);
      }
    } else {
      console.log('Site is not blocked:', url);
    }
  }

  async updateBlockingRules() {
    // This will be used for declarativeNetRequest rules in future phases
    // For now, we handle blocking through tab redirection
    console.log('Blocking rules updated for', this.blockedSites.size, 'sites');
  }

  async saveSettings() {
    try {
      await chrome.storage.local.set({
        blockedSites: Array.from(this.blockedSites),
        isEnabled: this.isEnabled,
        settings: this.settings,
        temporaryUnblocks: Array.from(this.temporaryUnblocks),
        usageTracking: Array.from(this.usageTracking),
        dailyLimits: Array.from(this.dailyLimits),
        focusMode: this.focusMode,
        masterPassword: this.masterPassword,
        // Phase 4: Content filtering data
        adultContentSites: Array.from(this.adultContentSites),
        blockedKeywords: Array.from(this.blockedKeywords),
        contentFiltering: this.contentFiltering,
        protectionDisabling: this.protectionDisabling
      });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  async handleFirstInstall() {
    // Set up default settings and show welcome notification
    await this.saveSettings();

    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'Welcome to FocusGuard Pro!',
      message: 'Click the extension icon to start blocking distracting websites.'
    });

    // Open options page
    chrome.tabs.create({ url: chrome.runtime.getURL('options/options.html') });
  }
}

// Initialize the background service
const focusGuard = new FocusGuardBackground();
