# FocusGuard Pro - Complete Testing Guide (Phases 1-3)

## Pre-Installation Setup

1. **Generate Icons**:
   - Open `create-icons.html` in your browser
   - Download the three generated icons (icon16.png, icon48.png, icon128.png)
   - Place them in the `icons/` folder

## Installation Steps

1. **Load Extension in Chrome**:
   - Open Chrome browser
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the FocusGuard Pro folder
   - Extension should appear in your extensions list

2. **Verify Installation**:
   - Look for the FocusGuard Pro icon in the Chrome toolbar
   - If not visible, click the puzzle piece icon and pin FocusGuard Pro

## Basic Functionality Tests

### Test 1: Popup Interface
1. Click the FocusGuard Pro icon
2. Verify popup opens with:
   - Header showing "FocusGuard Pro" with shield icon
   - Status indicator showing "Active"
   - Main toggle switch (should be active/blue)
   - Input field for adding websites
   - Current site section (if on a valid website)
   - Empty blocked sites list initially
   - Settings and Focus Mode buttons

### Test 2: Add a Blocked Site
1. In the popup, enter "example.com" in the input field
2. Click "Block" button
3. Verify:
   - Site appears in the blocked sites list
   - Site count updates
   - Success notification (check console if not visible)

### Test 3: Test Site Blocking
1. Navigate to `http://example.com` in a new tab
2. Verify:
   - Page redirects to the blocked page
   - Blocked page shows FocusGuard Pro branding
   - Shows the blocked URL
   - Displays productivity tips
   - Has "Go Back" and "Unblock Site" buttons

### Test 4: Unblock Site
1. On the blocked page, click "Unblock Site"
2. Select "Remove permanently"
3. Verify:
   - Site is removed from blocked list
   - Can now access the site normally

### Test 5: Settings Page
1. Right-click the extension icon
2. Select "Options"
3. Verify:
   - Settings page opens in new tab
   - Navigation sidebar works
   - Overview shows statistics
   - Block List section allows adding/removing sites
   - General settings toggles work

### Test 6: Current Site Blocking
1. Navigate to any website (e.g., google.com)
2. Open the popup
3. Verify current site section shows the website
4. Click "Block" button for current site
5. Verify site gets blocked immediately

## Advanced Tests

### Test 7: Extension Toggle
1. Open popup
2. Click the main toggle switch to disable
3. Verify:
   - Status changes to "Inactive"
   - Toggle switch becomes gray
   - Previously blocked sites are accessible

### Test 8: Settings Persistence
1. Open settings page
2. Toggle various settings (notifications, incognito mode, etc.)
3. Close and reopen settings
4. Verify settings are saved

### Test 9: Data Export/Import
1. In settings, add several blocked sites
2. Click "Export Data"
3. Verify JSON file downloads
4. Click "Clear All Data" and confirm
5. Click "Import Data" and select the exported file
6. Verify all data is restored

## Troubleshooting

### Common Issues:

1. **Extension doesn't load**:
   - Check that all files are present
   - Verify manifest.json syntax
   - Check browser console for errors

2. **Popup doesn't open**:
   - Verify popup files exist
   - Check for JavaScript errors in popup console

3. **Sites not blocking**:
   - Check background script console for errors
   - Verify extension has necessary permissions
   - Test with simple domains first

4. **Icons not showing**:
   - Generate icons using create-icons.html
   - Place in icons/ folder with correct names

### Debug Console Access:
- **Background Script**: Go to chrome://extensions/, find FocusGuard Pro, click "service worker"
- **Popup**: Right-click popup → Inspect
- **Options Page**: F12 on options page
- **Content Script**: F12 on any webpage

## Expected Behavior Summary

✅ **Working Features**:
- Basic website blocking via redirection
- Add/remove sites from block list
- Toggle extension on/off
- Popup interface with site management
- Settings page with configuration options
- Blocked page with user-friendly interface
- Data export/import functionality
- Current site blocking from popup

❌ **Not Yet Implemented** (Future Phases):
- Password protection
- Time-based unblocking
- Focus mode/Pomodoro timer
- Adult content filtering
- Keyword blocking
- Advanced analytics
- Cloud sync

## Performance Notes
- Extension should have minimal impact on browsing speed
- Storage operations should be fast
- UI should be responsive
- No memory leaks or excessive resource usage

## Security Considerations
- All data stored locally in Chrome storage
- No external network requests
- No sensitive data collection
- User has full control over blocked sites list

---

## Phase 2 Testing: Time Management Features

### Test 10: Focus Mode Interface
1. Click "Focus Mode" button in popup
2. Verify focus mode window opens with:
   - Circular timer display showing 25:00
   - "Work Session" label
   - Start button
   - Quick settings for durations
   - Focus tips section

### Test 11: Focus Session Functionality
1. Click "Start Focus" button
2. Verify:
   - Timer starts counting down
   - Progress ring animates
   - Session type shows "Work Session"
   - Pause and Stop buttons appear
   - Visual state changes to active

### Test 12: Focus Session Controls
1. During active session, click "Pause"
2. Verify timer pauses and button changes to "Resume"
3. Click "Resume" and verify timer continues
4. Click "Stop" and verify session ends

### Test 13: Automatic Break Transition
1. Start a 1-minute work session (change duration in settings)
2. Wait for session to complete
3. Verify:
   - Automatic transition to break
   - Timer shows break duration
   - Visual state changes to break mode
   - Notification appears

### Test 14: Time Limits Configuration
1. Go to settings → Time Limits
2. Enable time limits toggle
3. Set default daily limit to 30 minutes
4. Verify settings are saved

### Test 15: Usage Tracking
1. Visit a website for a few minutes
2. Check if time is being tracked (console logs)
3. Verify usage data is stored

## Phase 3 Testing: Security Features

### Test 16: Master Password Setup
1. Go to settings → Password section
2. Enter a password (minimum 6 characters)
3. Click "Update Password"
4. Verify success notification

### Test 17: Password Protection for Permanent Unblock
1. Block a site (e.g., example.com)
2. Visit the blocked site
3. Click "Unblock Site" → "Remove permanently"
4. Verify password prompt appears
5. Enter correct password and verify unblock works
6. Try with wrong password and verify it fails

### Test 18: OTP Temporary Unblock
1. Block a site and visit it
2. Click "Unblock Site" → "15 minutes"
3. Verify OTP is displayed in prompt
4. Enter the OTP correctly
5. Verify site is unblocked temporarily
6. Check that site becomes blocked again after time expires

### Test 19: OTP Security
1. Generate an OTP for temporary unblock
2. Try entering wrong OTP
3. Verify access is denied
4. Try using same OTP after 10+ minutes
5. Verify OTP has expired

### Test 20: Security Settings Integration
1. Enable password protection in settings
2. Verify password is required for:
   - Permanent site unblocks
   - Sensitive settings changes
3. Disable password protection
4. Verify password is no longer required

## Advanced Integration Tests

### Test 21: Focus Mode + Site Blocking
1. Start focus mode
2. Try visiting any website during work session
3. Verify all sites are blocked during focus
4. Start break and verify sites are accessible
5. End focus mode and verify normal blocking resumes

### Test 22: Time Limits + Daily Reset
1. Set a very low daily limit (1 minute)
2. Spend time on a site until limit is reached
3. Verify site gets automatically blocked
4. Change system date to next day
5. Verify usage resets and site is accessible again

### Test 23: Temporary Unblock + Time Limits
1. Set daily limit for a site
2. Exceed the limit (site gets blocked)
3. Use OTP to temporarily unblock
4. Verify site is accessible during unblock period
5. Verify limit enforcement resumes after unblock expires

### Test 24: Data Persistence
1. Configure all settings (passwords, limits, focus mode)
2. Add several blocked sites
3. Restart browser/reload extension
4. Verify all settings and data are preserved

### Test 25: Error Handling
1. Try invalid operations (empty passwords, invalid OTPs)
2. Verify appropriate error messages
3. Test with network disconnected
4. Verify graceful degradation

## Performance Tests

### Test 26: Memory Usage
1. Monitor extension memory usage
2. Run focus sessions, block/unblock sites
3. Verify no memory leaks
4. Check background script performance

### Test 27: Timer Accuracy
1. Start focus session
2. Compare timer with system clock
3. Verify accuracy within 1-2 seconds
4. Test pause/resume accuracy

## Expected Results Summary

### ✅ **Phase 1 Features Working**
- Basic site blocking and management
- Popup interface and settings page
- Data persistence and export/import

### ✅ **Phase 2 Features Working**
- Focus mode with Pomodoro timer
- Visual progress indicators
- Automatic session transitions
- Time tracking and daily limits
- Usage analytics

### ✅ **Phase 3 Features Working**
- Master password system
- OTP temporary unblocks
- Security integration
- Time-based access control
- Multi-level authentication

If all tests pass, **Phases 1-3 implementation is successful!** 🎉🎯🔐
