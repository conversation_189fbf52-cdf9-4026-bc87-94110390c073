// FocusGuard Pro - Blocked Page JavaScript

class BlockedPage {
  constructor() {
    this.blockedUrl = '';
    this.blockReason = 'site'; // Default reason
    this.blockDetails = '';
    this.init();
  }

  init() {
    // Get blocked URL from query parameters
    this.getBlockedUrl();

    // Update UI with blocked site info
    this.updateBlockedSiteInfo();

    // Setup event listeners
    this.setupEventListeners();

    // Update timestamp
    this.updateTimestamp();
  }

  getBlockedUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    this.blockedUrl = urlParams.get('blocked') || 'Unknown site';
    this.blockReason = urlParams.get('reason') || 'site';
    this.blockDetails = urlParams.get('details') || '';
  }

  updateBlockedSiteInfo() {
    const blockedUrlElement = document.getElementById('blockedUrl');
    if (blockedUrlElement) {
      blockedUrlElement.textContent = this.blockedUrl;
    }

    // Phase 4: Update UI based on block reason
    this.updateBlockReasonUI();
  }

  // Phase 4: Update UI based on block reason
  updateBlockReasonUI() {
    const titleElement = document.querySelector('.blocked-title');
    const messageElement = document.querySelector('.blocked-message');
    const iconElement = document.querySelector('.blocked-icon');
    const unblockBtn = document.getElementById('unblockBtn');

    switch (this.blockReason) {
      case 'adult':
        if (titleElement) titleElement.textContent = 'Adult Content Blocked';
        if (iconElement) iconElement.textContent = '🔞';
        if (messageElement) {
          messageElement.innerHTML = `
            <p>This page contains adult content and has been blocked for your protection.</p>
            <div class="block-details">
              <strong>Detected:</strong> ${this.blockDetails}
            </div>
            <p>Adult content blocking cannot be disabled with temporary unblocks.</p>
          `;
        }
        // Hide unblock button for adult content
        if (unblockBtn) unblockBtn.style.display = 'none';
        break;

      case 'keyword':
        if (titleElement) titleElement.textContent = 'Blocked Keywords Detected';
        if (iconElement) iconElement.textContent = '🚫';
        if (messageElement) {
          messageElement.innerHTML = `
            <p>This search or page contains blocked keywords and has been filtered.</p>
            <div class="block-details">
              <strong>Blocked keyword:</strong> ${this.blockDetails}
            </div>
            <p>Try searching for something else or modify your search terms.</p>
          `;
        }
        // Hide unblock button for keyword blocks
        if (unblockBtn) unblockBtn.style.display = 'none';
        break;

      case 'site':
      default:
        // Default site blocking - keep existing UI
        if (titleElement) titleElement.textContent = 'Website Blocked';
        if (iconElement) iconElement.textContent = '🛡️';
        if (messageElement) {
          messageElement.innerHTML = `
            <p>This website has been blocked to help you stay focused and productive.</p>
            <p>You can temporarily unblock it or adjust your settings if needed.</p>
          `;
        }
        break;
    }

    // Add appropriate styling based on block reason
    const container = document.querySelector('.blocked-container');
    if (container) {
      container.classList.remove('adult-block', 'keyword-block', 'site-block');
      container.classList.add(`${this.blockReason}-block`);
    }
  }

  updateTimestamp() {
    const timestampElement = document.getElementById('blockTimestamp');
    if (timestampElement) {
      const now = new Date();
      timestampElement.textContent = now.toLocaleTimeString();
    }
  }

  setupEventListeners() {
    // Go back button
    const goBackBtn = document.getElementById('goBackBtn');
    if (goBackBtn) {
      goBackBtn.addEventListener('click', () => this.goBack());
    }

    // Unblock button
    const unblockBtn = document.getElementById('unblockBtn');
    if (unblockBtn) {
      unblockBtn.addEventListener('click', () => this.showUnblockModal());
    }

    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => this.openSettings());
    }

    // Help button
    const helpBtn = document.getElementById('helpBtn');
    if (helpBtn) {
      helpBtn.addEventListener('click', () => this.showHelp());
    }

    // Disable link
    const disableLink = document.getElementById('disableLink');
    if (disableLink) {
      disableLink.addEventListener('click', (e) => {
        e.preventDefault();
        this.disableForSession();
      });
    }

    // Modal close button
    const modalClose = document.getElementById('modalClose');
    if (modalClose) {
      modalClose.addEventListener('click', () => this.hideUnblockModal());
    }

    // Modal overlay click to close
    const modalOverlay = document.getElementById('unblockModal');
    if (modalOverlay) {
      modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
          this.hideUnblockModal();
        }
      });
    }

    // Unblock option buttons
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const duration = btn.getAttribute('data-duration');
        this.unblockSite(duration);
      });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideUnblockModal();
      } else if (e.key === 'Enter' && e.ctrlKey) {
        this.goBack();
      }
    });
  }

  goBack() {
    // Try to go back in history
    if (window.history.length > 1) {
      window.history.back();
    } else {
      // If no history, go to a safe default page
      window.location.href = 'https://www.google.com';
    }
  }

  showUnblockModal() {
    const modal = document.getElementById('unblockModal');
    if (modal) {
      modal.classList.add('show');
    }
  }

  hideUnblockModal() {
    const modal = document.getElementById('unblockModal');
    if (modal) {
      modal.classList.remove('show');
    }
  }

  async unblockSite(duration) {
    try {
      const domain = this.extractDomain(this.blockedUrl);

      if (duration === 'permanent') {
        // Check if password protection is enabled
        const securityResponse = await chrome.runtime.sendMessage({
          action: 'getSecurityStatus'
        });

        if (securityResponse.passwordProtection && securityResponse.hasPassword) {
          // Require password for permanent unblock
          const password = prompt('Enter master password to permanently unblock this site:');
          if (!password) {
            this.hideUnblockModal();
            return;
          }

          const verifyResponse = await chrome.runtime.sendMessage({
            action: 'verifyMasterPassword',
            password: password
          });

          if (!verifyResponse.valid) {
            this.showErrorMessage('Invalid password');
            this.hideUnblockModal();
            return;
          }
        }

        // Remove site from blocked list permanently
        const response = await chrome.runtime.sendMessage({
          action: 'removeBlockedSite',
          url: domain
        });

        if (response && response.success) {
          this.showSuccessMessage('Site unblocked permanently');
          setTimeout(() => {
            window.location.href = this.blockedUrl;
          }, 1500);
        } else {
          this.showErrorMessage('Failed to unblock site');
        }
      } else {
        // Temporary unblock with OTP
        const durationMinutes = parseInt(duration);

        // Generate OTP
        const otpResponse = await chrome.runtime.sendMessage({
          action: 'generateOTP'
        });

        if (otpResponse && otpResponse.otp) {
          const userOTP = prompt(`Enter this OTP to unblock for ${durationMinutes} minutes: ${otpResponse.otp}`);

          if (userOTP === otpResponse.otp) {
            // Create temporary unblock
            const unblockResponse = await chrome.runtime.sendMessage({
              action: 'createTemporaryUnblock',
              domain: domain,
              durationMinutes: durationMinutes,
              otp: otpResponse.otp
            });

            if (unblockResponse && unblockResponse.success) {
              this.showSuccessMessage(`Site unblocked for ${durationMinutes} minutes`);
              setTimeout(() => {
                window.location.href = this.blockedUrl;
              }, 1500);
            } else {
              this.showErrorMessage('Failed to create temporary unblock');
            }
          } else {
            this.showErrorMessage('Invalid OTP');
          }
        } else {
          this.showErrorMessage('Failed to generate OTP');
        }
      }
    } catch (error) {
      console.error('Error unblocking site:', error);
      this.showErrorMessage('Failed to unblock site');
    }

    this.hideUnblockModal();
  }

  async disableForSession() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'toggleBlocking'
      });

      if (response && response.success) {
        this.showSuccessMessage('FocusGuard disabled for this session');
        setTimeout(() => {
          window.location.href = this.blockedUrl;
        }, 1500);
      } else {
        this.showErrorMessage('Failed to disable FocusGuard');
      }
    } catch (error) {
      console.error('Error disabling for session:', error);
      this.showErrorMessage('Failed to disable FocusGuard');
    }
  }

  openSettings() {
    chrome.tabs.create({ url: chrome.runtime.getURL('options/options.html') });
  }

  showHelp() {
    // Create a simple help modal or redirect to help page
    alert('FocusGuard Pro Help\n\n' +
      '• Click "Go Back" to return to the previous page\n' +
      '• Click "Unblock Site" to temporarily or permanently unblock this website\n' +
      '• Click "Settings" to configure your blocking preferences\n' +
      '• Use "Disable for this session" to turn off blocking temporarily\n\n' +
      'For more help, visit the extension settings page.');
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
      // If URL parsing fails, assume it's already a domain
      return url.replace(/^www\./, '');
    }
  }

  showSuccessMessage(message) {
    this.showNotification(message, 'success');
  }

  showErrorMessage(message) {
    this.showNotification(message, 'error');
  }

  showInfoMessage(message) {
    this.showNotification(message, 'info');
  }

  showNotification(message, type = 'info') {
    // Create a simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Add styles
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      animation: slideIn 0.3s ease;
      max-width: 300px;
      word-wrap: break-word;
    `;

    // Set background color based on type
    switch (type) {
      case 'success':
        notification.style.background = '#48bb78';
        break;
      case 'error':
        notification.style.background = '#f56565';
        break;
      case 'info':
      default:
        notification.style.background = '#667eea';
        break;
    }

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      @keyframes slideOut {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  // Add some motivational quotes that rotate
  addMotivationalQuote() {
    const quotes = [
      "Focus is a matter of deciding what things you're not going to do.",
      "The successful warrior is the average person with laser-like focus.",
      "Concentrate all your thoughts upon the work at hand.",
      "Focus on being productive instead of busy.",
      "Where focus goes, energy flows and results show."
    ];

    const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];

    // Add quote to the page if there's a container for it
    const quoteContainer = document.querySelector('.blocked-message');
    if (quoteContainer) {
      const quoteElement = document.createElement('div');
      quoteElement.style.cssText = `
        margin-top: 16px;
        font-style: italic;
        font-size: 14px;
        opacity: 0.8;
      `;
      quoteElement.textContent = `"${randomQuote}"`;
      quoteContainer.appendChild(quoteElement);
    }
  }
}

// Initialize the blocked page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const blockedPage = new BlockedPage();

  // Add motivational quote
  blockedPage.addMotivationalQuote();
});
