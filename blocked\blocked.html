<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Blocked - FocusGuard Pro</title>
    <link rel="stylesheet" href="blocked.css">
</head>
<body>
    <div class="blocked-container">
        <!-- Header -->
        <header class="blocked-header">
            <div class="logo">
                <div class="shield-icon">🛡️</div>
                <h1>FocusGuard Pro</h1>
            </div>
        </header>

        <!-- Main Content -->
        <main class="blocked-content">
            <div class="blocked-icon">
                <div class="icon-circle">
                    <span class="block-symbol">🚫</span>
                </div>
            </div>

            <h1 class="blocked-title">Website Blocked</h1>
            <p class="blocked-message">
                This website has been blocked to help you stay focused and productive.
            </p>

            <div class="blocked-site-info">
                <div class="site-details">
                    <h3>Blocked Site:</h3>
                    <p class="blocked-url" id="blockedUrl">Loading...</p>
                </div>
                <div class="block-time">
                    <h3>Blocked Since:</h3>
                    <p class="block-timestamp" id="blockTimestamp">Just now</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-primary" id="goBackBtn">
                    <span class="btn-icon">←</span>
                    Go Back
                </button>
                <button class="btn btn-secondary" id="unblockBtn">
                    <span class="btn-icon">🔓</span>
                    Unblock Site
                </button>
            </div>

            <!-- Productivity Tips -->
            <div class="productivity-tips">
                <h3>💡 Stay Productive Instead</h3>
                <div class="tips-grid">
                    <div class="tip-card">
                        <div class="tip-icon">📚</div>
                        <h4>Learn Something New</h4>
                        <p>Try online courses or educational content</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">💪</div>
                        <h4>Take a Break</h4>
                        <p>Step away from the screen and stretch</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">✅</div>
                        <h4>Complete Tasks</h4>
                        <p>Focus on your to-do list instead</p>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="quick-links">
                <h3>🔗 Productive Alternatives</h3>
                <div class="links-grid">
                    <a href="https://www.wikipedia.org" class="quick-link" target="_blank">
                        <span class="link-icon">📖</span>
                        Wikipedia
                    </a>
                    <a href="https://www.khanacademy.org" class="quick-link" target="_blank">
                        <span class="link-icon">🎓</span>
                        Khan Academy
                    </a>
                    <a href="https://www.coursera.org" class="quick-link" target="_blank">
                        <span class="link-icon">💻</span>
                        Coursera
                    </a>
                    <a href="https://www.ted.com" class="quick-link" target="_blank">
                        <span class="link-icon">🎤</span>
                        TED Talks
                    </a>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="blocked-footer">
            <div class="footer-actions">
                <button class="btn btn-ghost" id="settingsBtn">
                    <span class="btn-icon">⚙️</span>
                    Settings
                </button>
                <button class="btn btn-ghost" id="helpBtn">
                    <span class="btn-icon">❓</span>
                    Help
                </button>
            </div>
            <p class="footer-text">
                Blocked by FocusGuard Pro • 
                <a href="#" id="disableLink">Disable for this session</a>
            </p>
        </footer>
    </div>

    <!-- Unblock Modal -->
    <div class="modal-overlay" id="unblockModal">
        <div class="modal">
            <div class="modal-header">
                <h3>Unblock Website</h3>
                <button class="modal-close" id="modalClose">×</button>
            </div>
            <div class="modal-content">
                <p>How long would you like to unblock this website?</p>
                <div class="unblock-options">
                    <button class="option-btn" data-duration="15">15 minutes</button>
                    <button class="option-btn" data-duration="30">30 minutes</button>
                    <button class="option-btn" data-duration="60">1 hour</button>
                    <button class="option-btn" data-duration="1440">24 hours</button>
                    <button class="option-btn" data-duration="permanent">Remove permanently</button>
                </div>
                <div class="modal-note">
                    <p><strong>Note:</strong> This will temporarily remove the block. The site will be blocked again after the selected time period.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="blocked.js"></script>
</body>
</html>
